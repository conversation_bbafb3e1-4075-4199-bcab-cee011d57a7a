import {
  Button,
  CurrencyInput,
  Drawer,
  Input,
  Label,
  Switch,
  Text,
} from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { AdminCreateEmployee, Company } from "../../../../../types"
import { getCurrencySymbol } from "../../../../../lib/data/currencies"

interface EmployeeCreateFormProps {
  company: Company
  handleSubmit: (data: {
    first_name: string
    last_name: string
    email: string
    phone: string
    is_admin: boolean
    spending_limit: number
  }) => void
  isLoading: boolean
}

export function EmployeeCreateForm({
  company,
  handleSubmit,
  isLoading,
}: EmployeeCreateFormProps) {
  const { t } = useTranslation()
  
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    is_admin: false,
    spending_limit: 0,
  })

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSubmit(formData)
  }

  return (
    <form onSubmit={onSubmit} className="flex flex-col gap-4">
      <div className="grid grid-cols-1 gap-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="first_name">{t("companies.employees.form.firstName")}</Label>
            <Input
              id="first_name"
              value={formData.first_name}
              onChange={(e) =>
                setFormData({ ...formData, first_name: e.target.value })
              }
              required
            />
          </div>
          <div>
            <Label htmlFor="last_name">{t("companies.employees.form.lastName")}</Label>
            <Input
              id="last_name"
              value={formData.last_name}
              onChange={(e) =>
                setFormData({ ...formData, last_name: e.target.value })
              }
              required
            />
          </div>
        </div>

        <div>
          <Label htmlFor="email">{t("companies.employees.form.email")}</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            required
          />
        </div>

        <div>
          <Label htmlFor="phone">{t("companies.employees.form.phone")}</Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) =>
              setFormData({ ...formData, phone: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="spending_limit">{t("companies.employees.form.spendingLimit")}</Label>
          <CurrencyInput
            id="spending_limit"
            symbol={getCurrencySymbol(company.currency_code)}
            code={company.currency_code}
            value={formData.spending_limit}
            onValueChange={(value) =>
              setFormData({ ...formData, spending_limit: value || 0 })
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Text className="font-medium">{t("companies.employees.form.adminAccess")}</Text>
            <Text className="text-sm text-ui-fg-subtle">
              {t("companies.employees.form.adminAccessDescription")}
            </Text>
          </div>
          <Switch
            checked={formData.is_admin}
            onCheckedChange={(checked) =>
              setFormData({ ...formData, is_admin: checked })
            }
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Drawer.Close asChild>
          <Button variant="secondary" type="button">
            {t("common.cancel")}
          </Button>
        </Drawer.Close>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? t("common.creating") : t("companies.employees.create.submit")}
        </Button>
      </div>
    </form>
  )
}

import {
  Button,
  CurrencyInput,
  Drawer,
  Input,
  Label,
  Switch,
  Text,
} from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { AdminCreateEmployee, Company } from "../../../../../types"
import { getCurrencySymbol } from "../../../../../lib/data/currencies"

interface EmployeeCreateFormProps {
  company: Company
  handleSubmit: (data: {
    first_name: string
    last_name: string
    email: string
    phone: string
    is_admin: boolean
    spending_limit: number
  }) => void
  isLoading: boolean
}

export function EmployeeCreateForm({
  company,
  handleSubmit,
  isLoading,
}: EmployeeCreateFormProps) {
  const { t } = useTranslation()
  
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    is_admin: false,
    spending_limit: 0,
  })

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSubmit(formData)
  }

  return (
    <form onSubmit={onSubmit}>
      <Drawer.Body className="flex flex-col p-4 gap-6">
        <div className="flex flex-col gap-3">
          <h2 className="h2-core">{t("companies.employees.form.details")}</h2>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.firstName")}
            </Label>
            <Input
              type="text"
              value={formData.first_name}
              onChange={(e) =>
                setFormData({ ...formData, first_name: e.target.value })
              }
              placeholder={t("companies.employees.form.firstNamePlaceholder")}
              required
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.lastName")}
            </Label>
            <Input
              type="text"
              value={formData.last_name}
              onChange={(e) =>
                setFormData({ ...formData, last_name: e.target.value })
              }
              placeholder={t("companies.employees.form.lastNamePlaceholder")}
              required
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.email")}
            </Label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              placeholder={t("companies.employees.form.emailPlaceholder")}
              required
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.phone")}
            </Label>
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              placeholder={t("companies.employees.form.phonePlaceholder")}
            />
          </div>
        </div>
        <div className="flex flex-col gap-3">
          <h2 className="h2-core">{t("companies.employees.form.permissions")}</h2>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.spendingLimit")} ({company.currency_code?.toUpperCase() || "USD"})
            </Label>
            <CurrencyInput
              symbol={getCurrencySymbol(company.currency_code)}
              code={company.currency_code}
              value={formData.spending_limit}
              onValueChange={(value) =>
                setFormData({ ...formData, spending_limit: value || 0 })
              }
              placeholder={t("companies.employees.form.spendingLimitPlaceholder")}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.adminAccess")}
            </Label>
            <div className="flex items-center justify-between">
              <div>
                <Text className="font-medium">{t("companies.employees.form.isAdmin")}</Text>
                <Text className="text-sm text-ui-fg-subtle">
                  {t("companies.employees.form.adminAccessDescription")}
                </Text>
              </div>
              <Switch
                checked={formData.is_admin}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_admin: checked })
                }
              />
            </div>
          </div>
        </div>
      </Drawer.Body>
      <Drawer.Footer>
        <Drawer.Close asChild>
          <Button variant="secondary">{t("common.cancel")}</Button>
        </Drawer.Close>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? t("companies.employees.form.saving") : t("companies.employees.form.save")}
        </Button>
      </Drawer.Footer>
    </form>
  )
}
